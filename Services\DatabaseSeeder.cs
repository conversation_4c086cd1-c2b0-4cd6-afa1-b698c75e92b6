using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    public class DatabaseSeeder
    {
        private readonly IDataService _dataService;

        public DatabaseSeeder()
        {
            _dataService = new SqliteDataService();
        }

        public async Task SeedAllDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🌱 بدء إضافة البيانات الأساسية...");

                await SeedSectorsAsync();
                await SeedDriversAndVehiclesAsync();
                await SeedProjectsAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم إضافة جميع البيانات الأساسية بنجاح!");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة البيانات: {ex.Message}");
            }
        }

        public async Task SeedDriversAndVehiclesAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🚗 بدء إضافة بيانات السائقين والمركبات...");

                // الحصول على القطاعات لربطها بالسائقين
                var sectors = await _dataService.GetSectorsAsync();
                var sectorMap = new Dictionary<string, int>();

                foreach (var sector in sectors)
                {
                    sectorMap[sector.Name] = sector.Id;
                }

                // بيانات السائقين والمركبات
                var driversData = new[]
                {
                    new { Id = 71, Name = "محمد عبدالعزيز مقبل عزيز", Sector = "التعليم", Rank = "ضابط مشاريع", CardNumber = "17010026546", Phone = "777407640", CardType = "شخصية", Code = "130" },
                    new { Id = 76, Name = "جمال علي علي عبدالله الفاطمي", Sector = "الصحة والحماية الاجتماعية", Rank = "ضابط مشاريع", CardNumber = "17010097208", Phone = "770722006", CardType = "شخصية", Code = "106" },
                    new { Id = 77, Name = "احمد صالح احمد حيمد", Sector = "الصحة والحماية الاجتماعية", Rank = "ضابط مشاريع", CardNumber = "17010097208", Phone = "774438120", CardType = "شخصية", Code = "101" },
                    new { Id = 78, Name = "علي علي احمد العمدي", Sector = "الصحة والحماية الاجتماعية", Rank = "سكرتاريه التغذية", CardNumber = "17010044025", Phone = "772328123", CardType = "شخصية", Code = "123" },
                    new { Id = 79, Name = "عبدالوهاب علي اسماعيل الخولاني", Sector = "الطرق", Rank = "", CardNumber = "", Phone = "", CardType = "", Code = "117" },
                    new { Id = 83, Name = "بشير عبده عزيز عبدالوهاب", Sector = "النقد مقابل العمل", Rank = "ضابط مشاريع", CardNumber = "1701005845", Phone = "777595467", CardType = "شخصية", Code = "105" },
                    new { Id = 84, Name = "غلاب عبدالاله علي عذبة", Sector = "النقد مقابل العمل", Rank = "ضابط مشاريع", CardNumber = "17010017329", Phone = "774113081", CardType = "شخصيىة", Code = "125" },
                    new { Id = 85, Name = "عبدالله علي ناصر الاضرعي", Sector = "النقد مقابل العمل", Rank = "ضابط مجتمعي", CardNumber = "17010044266", Phone = "771239969", CardType = "شخصية", Code = "114" },
                    new { Id = 86, Name = "بدر احمد صالح المبارزي", Sector = "النقد مقابل العمل", Rank = "محاسب", CardNumber = "17010001042", Phone = "771015656", CardType = "شخصية", Code = "104" },
                    new { Id = 88, Name = "محمد حمود عبده الوقاحي", Sector = "النقد مقابل العمل", Rank = "ضابط مجتمعي", CardNumber = "17010196538", Phone = "777821344", CardType = "شخصية", Code = "129" },
                    new { Id = 89, Name = "علي عبدالغني محمد الحرازي", Sector = "المياه والبيئة", Rank = "ضابط مشاريع", CardNumber = "", Phone = "", CardType = "", Code = "122" },
                    new { Id = 90, Name = "عدنان علوي علي السنباني", Sector = "المياه والبيئة", Rank = "ضابط مشاريع", CardNumber = "01010149503", Phone = "771653316", CardType = "شخصية", Code = "118" },
                    new { Id = 91, Name = "اسمهان صالح حسين الفقية", Sector = "المياه والبيئة", Rank = "ضابط مشاريع", CardNumber = "17000004388", Phone = "772064728", CardType = "شخصية", Code = "103" },
                    new { Id = 92, Name = "اسامه احمد سعيد فرحان", Sector = "المياه والبيئة", Rank = "ضابط محاسبي", CardNumber = "17010024414", Phone = "777453825", CardType = "شخصية", Code = "102" },
                    new { Id = 93, Name = "يحيى عبدالوهاب عبدالله يحيى الهندي", Sector = "المياه والبيئة", Rank = "محاسب", CardNumber = "17010130985", Phone = "771360366", CardType = "شخصية", Code = "135" },
                    new { Id = 100, Name = "علي احمد محمد عمران", Sector = "المراقبة والتقييم", Rank = "ضابط المراقبة والتقييم", CardNumber = "", Phone = "", CardType = "", Code = "121" },
                    new { Id = 106, Name = "ساميه احمد ناصر عوضه", Sector = "الصحة والحماية الاجتماعية", Rank = "ضابط مالي", CardNumber = "17000005954", Phone = "771929964", CardType = "شخصية", Code = "110" },
                    new { Id = 117, Name = "محمد عبدالله اسماعيل الثلاياء", Sector = "الصحة والحماية الاجتماعية", Rank = "ضابط الشكاوى", CardNumber = "17010015075", Phone = "770959624", CardType = "شخصية", Code = "131" },
                    new { Id = 119, Name = "عبدالله نعمان عبدالقادر علي", Sector = "الزراعة", Rank = "ضابط مشاريع", CardNumber = "01010201147", Phone = "771890930", CardType = "شخصية", Code = "116" },
                    new { Id = 121, Name = "عادل احمد عبدالله السوداني", Sector = "الزراعة", Rank = "ضابط مشاريع", CardNumber = "12010000411", Phone = "771732558", CardType = "شخصية", Code = "112" },
                    new { Id = 122, Name = "فردوس امين عبده القرشي", Sector = "الزراعة", Rank = "ضابط مجتمعي", CardNumber = "17000004450", Phone = "777075083", CardType = "شخصية", Code = "126" },
                    new { Id = 124, Name = "حميد عبدالله علي محمد السمهري", Sector = "الزراعة", Rank = "ضابط مشاريع", CardNumber = "17010057649", Phone = "777368837", CardType = "شخصية", Code = "108" },
                    new { Id = 125, Name = "نظير عبدالباري احمد الحصيني", Sector = "الزراعة", Rank = "ضابط مشاريع", CardNumber = "", Phone = "7777777", CardType = "", Code = "134" },
                    new { Id = 126, Name = "صفوان محمد يحيى النوفي", Sector = "الزراعة", Rank = "ضابط محاسبي", CardNumber = "17010170327", Phone = "777237387", CardType = "شخصية", Code = "111" },
                    new { Id = 137, Name = "عصام علي محمود العلفي", Sector = "الزراعة", Rank = "ضابط مشاريع", CardNumber = "00110008645", Phone = "777174045", CardType = "شخصية", Code = "120" },
                    new { Id = 138, Name = "عمرو حميد غانم محسن الحمراء", Sector = "الصحة والحماية الاجتماعية", Rank = "ضابط مشاريع", CardNumber = "5010126030", Phone = "773389033", CardType = "شخصية", Code = "124" },
                    new { Id = 143, Name = "عبدالله محمد احمد الأسود", Sector = "النقد مقابل العمل", Rank = "ضابط مشاريع", CardNumber = "17010054161", Phone = "777530929", CardType = "شخصية", Code = "115" },
                    new { Id = 152, Name = "فهد فرحان محمد عبدالله القريشي", Sector = "التعليم", Rank = "ضابط مشاريع", CardNumber = "17010018462", Phone = "771743512", CardType = "شخصية", Code = "127" },
                    new { Id = 155, Name = "عادل عبدالله سفيان كليب", Sector = "التدريب", Rank = "محاسب المشروع", CardNumber = "17010018334", Phone = "777621190", CardType = "شخصية", Code = "113" },
                    new { Id = 158, Name = "احمد رزق الله محمد الحاج", Sector = "الصحة والحماية الاجتماعية", Rank = "ضابط التعاقدات", CardNumber = "00110006478", Phone = "777796816", CardType = "شخصية", Code = "100" },
                    new { Id = 162, Name = "محمد احمد محمد الرعوي", Sector = "التعليم", Rank = "ضابط الفنية", CardNumber = "17010043873", Phone = "777368036", CardType = "شخصية", Code = "128" },
                    new { Id = 167, Name = "عدنان محمد علي ناصر النجحي", Sector = "الزراعة", Rank = "نائب مدير الفرع", CardNumber = "17010024137", Phone = "777600528", CardType = "شخصية", Code = "119" },
                    new { Id = 172, Name = "حسن عبدالله زيد عمران", Sector = "الزراعة", Rank = "استشاري", CardNumber = "17010113585", Phone = "770124258", CardType = "شخصيه", Code = "107" },
                    new { Id = 180, Name = "خالد محمد احمد الاحصب", Sector = "الصحة والحماية الاجتماعية", Rank = "ضابط مشاريع", CardNumber = "17010052406", Phone = "777902909", CardType = "شخصية", Code = "109" },
                    new { Id = 182, Name = "محمد حمود عبده الوقاحي", Sector = "الطرق", Rank = "ضابط مجتمعي", CardNumber = "17010196538", Phone = "777821344", CardType = "شخصية", Code = "129" },
                    new { Id = 184, Name = "فردوس امين عبده القرشي", Sector = "الطرق", Rank = "ضابط مجتمعي", CardNumber = "17000004450", Phone = "777075083", CardType = "شخصية", Code = "126" },
                    new { Id = 187, Name = "اسمهان صالح حسين الفقية", Sector = "الشكاوى والامتثال", Rank = "ضابط مشاريع", CardNumber = "17000004388", Phone = "772064728", CardType = "شخصية", Code = "103" }
                };

                int addedCount = 0;
                foreach (var driverData in driversData)
                {
                    try
                    {
                        // البحث عن معرف القطاع
                        int sectorId = 1; // القطاع الافتراضي
                        string sectorName = "القطاع";

                        if (sectorMap.ContainsKey(driverData.Sector))
                        {
                            sectorId = sectorMap[driverData.Sector];
                            sectorName = driverData.Sector;
                        }

                        // إنشاء سائق جديد
                        var driver = new Driver
                        {
                            Id = driverData.Id,
                            Name = driverData.Name,
                            PhoneNumber = driverData.Phone,
                            CardNumber = driverData.CardNumber,
                            CardType = driverData.CardType,
                            DriverCode = driverData.Code,
                            SectorId = sectorId,
                            SectorName = sectorName,
                            VehicleType = "غير محدد", // سيتم تحديثه لاحقاً
                            VehicleNumber = $"V-{driverData.Code}", // رقم مركبة افتراضي
                            CreatedAt = DateTime.Now,
                            IsActive = true
                        };

                        // إضافة السائق
                        var success = await _dataService.AddDriverAsync(driver);
                        if (success)
                        {
                            addedCount++;
                            System.Diagnostics.Debug.WriteLine($"✅ تم إضافة السائق: {driver.Name}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ فشل في إضافة السائق: {driver.Name}");
                        }

                        // إنشاء مركبة مرتبطة بالسائق
                        var vehicle = new Vehicle
                        {
                            OwnerName = driverData.Name,
                            IdCardNumber = driverData.CardNumber,
                            VehicleType = "غير محدد",
                            PlateNumber = $"V-{driverData.Code}",
                            DriverCode = driverData.Code,
                            SectorId = sectorId,
                            SectorName = sectorName,
                            CreatedAt = DateTime.Now,
                            IsActive = true
                        };

                        // إضافة المركبة
                        var vehicleSuccess = await _dataService.AddVehicleAsync(vehicle);
                        if (vehicleSuccess)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ تم إضافة المركبة: {vehicle.PlateNumber}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ فشل في إضافة المركبة: {vehicle.PlateNumber}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة السائق {driverData.Name}: {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إضافة {addedCount} سائق ومركبة بنجاح!");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة بيانات السائقين والمركبات: {ex.Message}");
            }
        }

        public async Task SeedSectorsAsync()
        {
            var existingSectors = await _dataService.GetSectorsAsync();
            if (existingSectors.Count > 0)
            {
                System.Diagnostics.Debug.WriteLine("📋 القطاعات موجودة مسبقاً");
                return;
            }

            var sectors = new List<Sector>
            {
                new Sector { Code = "QTAA", Name = "القطاع", IsActive = true },
                new Sector { Code = "training", Name = "التدريب", IsActive = true },
                new Sector { Code = "contracts", Name = "التعاقدات", IsActive = true },
                new Sector { Code = "education", Name = "التعليم", IsActive = true },
                new Sector { Code = "empowerment", Name = "التمكين", IsActive = true },
                new Sector { Code = "accounts", Name = "الحسابات", IsActive = true },
                new Sector { Code = "agriculture", Name = "الزراعة", IsActive = true },
                new Sector { Code = "complaints_compliance", Name = "الشكاوى والامتثال", IsActive = true },
                new Sector { Code = "health_social_protection", Name = "الصحة والحماية الاجتماعية", IsActive = true },
                new Sector { Code = "roads", Name = "الطرق", IsActive = true },
                new Sector { Code = "technical", Name = "الفنية", IsActive = true },
                new Sector { Code = "monitoring_evaluation", Name = "المراقبة والتقييم", IsActive = true },
                new Sector { Code = "water_environment", Name = "المياه والبيئة", IsActive = true },
                new Sector { Code = "cash_for_work", Name = "النقد مقابل العمل", IsActive = true }
            };

            foreach (var sector in sectors)
            {
                await _dataService.AddSectorAsync(sector);
            }

            System.Diagnostics.Debug.WriteLine($"📋 تم إضافة {sectors.Count} قطاع");
        }

        public async Task SeedProjectsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📋 بدء إضافة بيانات المشاريع...");

                // بيانات المشاريع - الجزء الأول
                var projectsData1 = new[]
                {
                    new { Number = "911-13727", Name = "تزويد المدارس المستهدفة بالأثاث والوسائل التعليمية لعزل السنة ومحزر وقشط رماع - مديرية وصاب العالي - محافظة ذمار", Days = 5 },
                    new { Number = "911-13717", Name = "مدرسة الثورة - قرية جبيت - عزلة السنه - مديرية وصاب العالي - محافظة ذمار", Days = 3 },
                    new { Number = "911-13720", Name = "مدرسة الوحدة بالجدد - قرية حورة - عزلة محزر - مديرية وصاب العالي - محافظة ذمار", Days = 4 },
                    new { Number = "911-13885", Name = "مدرسة الرويشان للبنات - قرية الزاهر - عزلة الزاهر - مديرية الزاهر - محافظة  البيضاء", Days = 6 },
                    new { Number = "911-13782", Name = "التحويلات النقدية المشروطة في التغذية عزلة الاجراف - مديرية وصاب السافل- ذمار", Days = 2 },
                    new { Number = "911-13917", Name = " التحويلات النقدية المشروطة في التغذية عزل وادي الخشب ،بني موسى،ربع المغارم- وصاب السافل- ذمار" },
                    new { Number = "911-13916", Name = "استكمال المرحلة الثالثة من أنشطة التحويلات النقدية المشروطة في التغذية عزل ال حسن،ال السادة،ال هادي،وذاهبة-السوادية-البيضاء" },
                    new { Number = "911-13824", Name = "النفقات الادارية غير المباشرة لمشاريع التحويلات النقدية المشروطة في التغذية - ذمار والبيضاء" },
                    new { Number = "911-13891", Name = "مشروع التحويلات النقدية المشروطة في التغذية عزلة ال غشام-مديرية الملاجم-البيضاء" },
                    new { Number = "911-13890", Name = "مشروع التحويلات النقدية المشروطة في التغذية عزلة بني سويد - مديرية ضوران انس - ذمار" },
                    new { Number = "911-13772", Name = "الدعم والتطوير المؤسسي لفرع المؤسسة المحلية للمياه والصرف الصحي بمدينة البيضاء -مديرية مدينة البيضاء - محافظة البيضاء" },
                    new { Number = "911-13575", Name = "بناء القدرات والتطوير المؤسسي للمؤسسة المحلية للمياه والصرف الصحي بمدينة البيضاء- م/ البيضاء" },
                    new { Number = "911-13484", Name = "مشروع مياه مدينة البيضاء - مدينة البيضاء - مديرية مدينة البيضاء - محافظة البيضاء" },
                    new { Number = "911-13797", Name = "دراسات وتصاميم مقترحات مشاريع المياه والاصحاح البيئي للمنحة الالمانية السادسة لفرع ذمار" },
                    new { Number = "911-13770", Name = " مشروع مياه مدينة البيضاء -مدينة البيضاء - مديرية مدينة البيضاء -محافظة البيضاء - المرحلة الثانية" },
                    new { Number = "911-13788", Name = "الصرف الصحي الموقعي لقرية نيسان - نيسان -الحداء - ذمار" },
                    new { Number = "911-13923", Name = "تأهيل طريق القفر وإنشاء المدرجات الزراعية والتدريب على تحسين سبل المعيشة - بني موسى - وصاب السافل - ذمار" },
                    new { Number = "911-13879", Name = "تأهيل طريق بيت حجر والقرى المجاورة - صباح - مديرية صباح - محافظة البيضاء" },
                    new { Number = "911-13904", Name = "تاهيل طريق اسول جبل الشرق -بني اسعد -ذمار" },
                    new { Number = "911-13903", Name = "تأهيل طريق اللفيق /بني اسعد/جبل الشرق /ذمار" },
                    new { Number = "911-13902", Name = "تأهيل طريق حجل -معكد /حمير ابزار /عتمة/ذمار" },
                    new { Number = "911-13901", Name = "تاهيل طريق  ارحب -قاعدة -الصفاء /عزلة قاعدة /وصاب العالي /ذمار" },
                    new { Number = "911-13841", Name = "دراسات لمشاريع المنحة الاضافية للاستجابة لتعزيز الامن الغذائي FSRRP -البيضاء" },
                    new { Number = "911-13893", Name = "إنشاء خزان حصاد مياه للري التكميلي غول عباس - رصابة - سفل جهران - ذمار" },
                    new { Number = "911-13909", Name = "اعادة تاهيل المدرجات  الزراعية في مديرية  وصاب السافل -ذمار" },
                    new { Number = "911-13850", Name = "انشاء حاجز مياه تخزيني - شعب اللال - طياب - عزلة طياب - ذي ناعم - البيضاء" },
                    new { Number = "911-13851", Name = "إنشاء خزان حصاد مياه للري التكميلي شعب الزاحفة - القرين- عزلة المنقطع - ذي ناعم - البيضاء" },
                    new { Number = "911-13895", Name = "إنشاء خزان حصاد مياه للري التكميلي - شعب الوعل - الشرفة - ثمن الرياشية - البيضاء" },
                    new { Number = "911-13896", Name = "إنشاء حاجز مياه تخزيني شعب المرادعة - دار خلبان - ثمن الرياشية - البيضاء" },
                    new { Number = "911-13907", Name = "تأهيل الطريق الزراعي -معبر -واسطه -جهران -ذمار" },
                    new { Number = "911-13852", Name = "تأهيل طريق السعد -الغربي والفجرة - عتمة - ذمار" },
                    new { Number = "911-13853", Name = "تأهيل طريق ربع المغارم - وصاب السافل - ذمار" },
                    new { Number = "911-13864", Name = "إنشاء خزان حصاد مياه للري التكميلي لقرية بيت سنان عزلة حمير ابزار - عتمة - ذمار" },
                    new { Number = "911-13865", Name = "إنشاء خزانات حصاد مياه للري التكميلي لقري الكرف والمقسومة عزلة حمير ابزار - عتمة - ذمار" },
                    new { Number = "911-13866", Name = "إنشاء خزان حصاد مياه للري التكميلي لقرية الاقرن - عزلة حمير ابزار - عتمة - ذمار" },
                    new { Number = "911-13869", Name = "إنشاء خزان حصاد مياه للري التكميلي الاهقان -الاجراف -وصاب السافل - ذمار" },
                    new { Number = "911-13871", Name = "اعادة تأهيل المراعي في حمير ابزار  - عتمة - ذمار" },
                    new { Number = "911-13872", Name = "اعادة تأهيل المراعي في الكينعة - المنار - ذمار" },
                    new { Number = "911-13842", Name = "دعم وبناء صغار مربي الثروة الحيوانية بالاصول الحيه والاعلاف في مديرية جهران - محافظة ذمار" },
                    new { Number = "911-13843", Name = "دعم وبناء قدرات مربي الثروة الحيوانية بالقطاعات العلفية مديرية جهران-محافظة ذمار" },
                    new { Number = "911-13844", Name = "دعم صغار مربي الثروة الحيوانية بالاصول الحيه والاعلاف وصاب السافل - محافظة ذمار" },
                    new { Number = "911-13857", Name = "السقايات الخاصة لقرية معيرم - بني سوادة - وصاب السافل- ذمار" },
                    new { Number = "911-13858", Name = "السقايات الخاصة لقرية الوطى - الشعاور والشماعه - وصاب السافل - ذمار" },
                    new { Number = "911-13873", Name = "إنشاء خزان حصاد مياه للري التكميلي المدرج (النقيل) - الاجراف - وصاب السافل - ذمار" },
                    new { Number = "911-13874", Name = "انشاء خزان حصاد مياه للري التكميلي الكاذية - الاجراف - وصاب السافل - ذمار" },
                    new { Number = "911-13875", Name = "إنشاء خزان حصاد مياه للري التكميلي سوير (عقمة الدارع ) - الاجراف - وصاب السافل - ذمار" }
                };

                int addedCount = await AddProjectsBatch(projectsData1);
                System.Diagnostics.Debug.WriteLine($"✅ تم إضافة {addedCount} مشروع من الدفعة الأولى");

                // إضافة باقي المشاريع
                var projectsSeeder = new ProjectsDataSeeder(_dataService);
                int remainingCount = await projectsSeeder.SeedRemainingProjectsAsync();

                System.Diagnostics.Debug.WriteLine($"✅ تم إضافة إجمالي {addedCount + remainingCount} مشروع بنجاح!");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة بيانات المشاريع: {ex.Message}");
            }
        }

        private async Task<int> AddProjectsBatch(dynamic[] projectsData)
        {
            int addedCount = 0;
            foreach (var projectData in projectsData)
            {
                try
                {
                    // إنشاء مشروع جديد
                    var project = new Project
                    {
                        ProjectNumber = projectData.Number,
                        ProjectName = projectData.Name,
                        CreatedAt = DateTime.Now,
                        IsActive = true
                    };

                    // إضافة المشروع
                    var success = await _dataService.AddProjectAsync(project);
                    if (success)
                    {
                        addedCount++;
                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة المشروع: {project.ProjectNumber}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ فشل في إضافة المشروع: {project.ProjectNumber}");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة المشروع {projectData.Number}: {ex.Message}");
                }
            }
            return addedCount;
        }

        public void Dispose()
        {
            _dataService?.Dispose();
        }
    }
}
